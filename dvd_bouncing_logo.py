import turtle
import random
import math
import time

# Set up the screen
screen = turtle.Screen()
screen.bgcolor("black")
screen.title("🎬 CLASSIC DVD BOUNCING LOGO 🎬")
screen.setup(width=1200, height=800)
screen.tracer(0)  # Turn off animation for manual control

# DVD Logo colors (classic rainbow cycle)
dvd_colors = [
    "#FF0000",  # Red
    "#FF4500",  # Orange Red
    "#FFA500",  # Orange
    "#FFD700",  # Gold
    "#FFFF00",  # Yellow
    "#ADFF2F",  # Green Yellow
    "#00FF00",  # Green
    "#00FF7F",  # Spring Green
    "#00FFFF",  # Cyan
    "#0080FF",  # Deep Sky Blue
    "#0000FF",  # Blue
    "#8000FF",  # Blue Violet
    "#FF00FF",  # Magenta
    "#FF1493",  # Deep Pink
]

class DVDLogo:
    def __init__(self):
        self.turtle = turtle.Turtle()
        self.turtle.speed(0)
        self.turtle.penup()
        self.turtle.shape("square")
        
        # Logo dimensions
        self.width = 120
        self.height = 60
        
        # Starting position (center of screen)
        self.x = 0
        self.y = 0
        
        # Velocity - make it slightly irregular to make corners harder to hit
        self.vx = 3.7  # Slightly non-integer speed
        self.vy = 2.3  # Different speed for y
        
        # Screen boundaries (accounting for logo size)
        self.max_x = 600 - self.width // 2
        self.min_x = -600 + self.width // 2
        self.max_y = 400 - self.height // 2
        self.min_y = -400 + self.height // 2
        
        # Color management
        self.color_index = 0
        self.current_color = dvd_colors[self.color_index]
        
        # Corner hit tracking
        self.corner_hits = 0
        self.total_bounces = 0
        
        # Create the DVD logo shape
        self.create_dvd_logo()
        
    def create_dvd_logo(self):
        """Create a custom DVD logo shape"""
        # Register a custom shape for the DVD logo
        dvd_shape = (
            # DVD text outline (simplified)
            (-60, -30), (-60, 30), (-40, 30), (-40, 10), (-50, 10), (-50, -10), 
            (-40, -10), (-40, -30), (-60, -30),
            # V shape
            (-35, -30), (-35, 30), (-25, 30), (-15, 0), (-5, 30), (5, 30), 
            (5, -30), (-5, -30), (-15, -10), (-25, -30), (-35, -30),
            # D shape
            (10, -30), (10, 30), (30, 30), (40, 20), (40, -20), (30, -30), (10, -30),
            # Video text (simplified)
            (45, -15), (45, 15), (60, 15), (60, -15), (45, -15)
        )
        
        screen.register_shape("dvd_logo", dvd_shape)
        self.turtle.shape("dvd_logo")
        self.turtle.color(self.current_color)
        
    def update_position(self):
        """Update logo position and handle bouncing"""
        # Update position
        self.x += self.vx
        self.y += self.vy
        
        # Check for wall collisions and bounce
        bounced = False
        
        # Horizontal walls
        if self.x >= self.max_x or self.x <= self.min_x:
            self.vx = -self.vx
            self.x = max(self.min_x, min(self.max_x, self.x))  # Keep within bounds
            bounced = True
            
        # Vertical walls  
        if self.y >= self.max_y or self.y <= self.min_y:
            self.vy = -self.vy
            self.y = max(self.min_y, min(self.max_y, self.y))  # Keep within bounds
            bounced = True
            
        # If bounced, change color and check for corner hit
        if bounced:
            self.total_bounces += 1
            self.change_color()
            self.check_corner_hit()
            
        # Update turtle position
        self.turtle.goto(self.x, self.y)
        
    def change_color(self):
        """Change to next color in the cycle"""
        self.color_index = (self.color_index + 1) % len(dvd_colors)
        self.current_color = dvd_colors[self.color_index]
        self.turtle.color(self.current_color)
        
    def check_corner_hit(self):
        """Check if logo hit a corner (rare event!)"""
        corner_threshold = 20  # How close to corner counts as a hit
        
        corners = [
            (self.max_x, self.max_y),    # Top right
            (self.max_x, self.min_y),    # Bottom right
            (self.min_x, self.max_y),    # Top left
            (self.min_x, self.min_y)     # Bottom left
        ]
        
        for corner_x, corner_y in corners:
            distance = math.sqrt((self.x - corner_x)**2 + (self.y - corner_y)**2)
            if distance <= corner_threshold:
                self.corner_hits += 1
                # Special celebration effect for corner hit!
                self.celebrate_corner_hit()
                break
                
    def celebrate_corner_hit(self):
        """Special effect when corner is hit"""
        print(f"🎉 CORNER HIT #{self.corner_hits}! 🎉")
        
        # Flash effect
        original_color = self.current_color
        for _ in range(6):
            self.turtle.color("white")
            screen.update()
            time.sleep(0.1)
            self.turtle.color("yellow")
            screen.update()
            time.sleep(0.1)
        self.turtle.color(original_color)
        
    def draw_stats(self):
        """Draw statistics on screen"""
        stats_turtle = turtle.Turtle()
        stats_turtle.speed(0)
        stats_turtle.penup()
        stats_turtle.hideturtle()
        stats_turtle.color("white")
        
        # Title
        stats_turtle.goto(0, 350)
        stats_turtle.write("🎬 CLASSIC DVD BOUNCING LOGO 🎬", 
                          align="center", font=("Courier", 20, "bold"))
        
        # Stats
        stats_turtle.goto(-580, 320)
        stats_turtle.write(f"Total Bounces: {self.total_bounces}", 
                          align="left", font=("Courier", 14, "normal"))
        
        stats_turtle.goto(-580, 290)
        stats_turtle.write(f"Corner Hits: {self.corner_hits} 🎯", 
                          align="left", font=("Courier", 14, "normal"))
        
        stats_turtle.goto(-580, 260)
        corner_percentage = (self.corner_hits / max(1, self.total_bounces)) * 100
        stats_turtle.write(f"Corner Hit Rate: {corner_percentage:.3f}%", 
                          align="left", font=("Courier", 14, "normal"))
        
        # Current speed display
        stats_turtle.goto(400, 320)
        speed = math.sqrt(self.vx**2 + self.vy**2)
        stats_turtle.write(f"Speed: {speed:.2f}", 
                          align="left", font=("Courier", 14, "normal"))
        
        stats_turtle.goto(400, 290)
        stats_turtle.write(f"Color: {self.current_color}", 
                          align="left", font=("Courier", 14, "normal"))
        
        # Instructions
        stats_turtle.goto(0, -350)
        stats_turtle.write("Watch for the rare corner hits! Close window to exit.", 
                          align="center", font=("Courier", 12, "italic"))
        
        del stats_turtle

def main():
    print("🎬 Starting Classic DVD Bouncing Logo Animation!")
    print("🎯 Watch carefully for corner hits - they're rare!")
    print("🌈 Logo changes color with each bounce")
    print("❌ Close the window to stop")
    
    # Create DVD logo
    dvd = DVDLogo()
    
    frame_count = 0
    
    try:
        while True:
            # Clear screen
            screen.clear()
            screen.bgcolor("black")
            
            # Update logo position
            dvd.update_position()
            
            # Draw stats every few frames for performance
            if frame_count % 10 == 0:
                dvd.draw_stats()
            
            # Add some visual flair - subtle screen border
            border_turtle = turtle.Turtle()
            border_turtle.speed(0)
            border_turtle.penup()
            border_turtle.hideturtle()
            border_turtle.color("gray")
            border_turtle.pensize(3)
            border_turtle.goto(-600, -400)
            border_turtle.pendown()
            border_turtle.goto(-600, 400)
            border_turtle.goto(600, 400)
            border_turtle.goto(600, -400)
            border_turtle.goto(-600, -400)
            del border_turtle
            
            # Update screen
            screen.update()
            frame_count += 1
            
            # Control animation speed (classic DVD speed)
            time.sleep(0.016)  # ~60 FPS
            
    except turtle.Terminator:
        print(f"\n🎬 Animation ended!")
        print(f"📊 Final Stats:")
        print(f"   Total Bounces: {dvd.total_bounces}")
        print(f"   Corner Hits: {dvd.corner_hits}")
        if dvd.total_bounces > 0:
            print(f"   Corner Hit Rate: {(dvd.corner_hits/dvd.total_bounces)*100:.3f}%")
        print("Thanks for watching! 🎉")
    except KeyboardInterrupt:
        print("Animation interrupted!")
    finally:
        screen.bye()

if __name__ == "__main__":
    main()
